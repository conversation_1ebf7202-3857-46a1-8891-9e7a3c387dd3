<template>
  <div
    class="laborContract"
    style="background: #f5f5f5; min-height: 100vh; padding: 20px 20px 0"
  >
    <div
      style="background: #ffffff; padding: 10px; border-radius: 10px"
      v-for="(item, index) in contractList"
      :key="index"
      @click="goSign(item.protocolId)"
    >
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <div>
          <div class="flex-box">
            <div class="contract__title">{{ item.protocolName }}</div>
            <div class="contract__time">2024-04-19</div>
          </div>
          <div class="flex-box" style="margin-top: 20px">
            <div class="contract_name">
              <span
                style="color: #d25957; background: #ffe9e5; margin-right: 5px"
                >发</span
              >
              <span style="color: #6a6a6a">{{ item.corporationName }}</span>
            </div>
            <div style="color: #f2c666">
              {{ formatterStatus(item.contractSignStatus) }}
            </div>
          </div>
        </div>
        <van-icon name="arrow" />
      </div>
    </div>
    <div v-if="!contractList.length" class="nothing-data">
      <img src="../../../assets/images/no-data.png" alt />
      <p>暂无任何待办事项</p>
    </div>
  </div>
</template>
<script>
import { Icon } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    [Icon.name]: Icon
  },
  data() {
    return {
      contractList: []
    }
  },
  created() {
    this.getContractList()
  },
  methods: {
    async getContractList() {
      const [err, r] = await client.supplierProtocolGetLaborContract()
      if (err) {
        handleError(err)
        return
      }
      this.contractList = r.data
    },
    formatterStatus(value) {
      switch (value) {
        case 'ACCEPT':
          return '待签署'
        case 'IN_PROCESS':
          return '处理中'
        case 'SUCCESS':
          return '签署成功'
        default:
          return '待签署'
      }
    },
    goSign(protocolId) {
      this.$router.push({
        path: '/laborContractSign',
        query: {
          protocolId
        }
      })
    }
  }
}
</script>
<style scoped>
.flex-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contract__title {
  width: 215px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.contract__time {
  color: #72777a;
  font-size: 12px;
}
.nothing-data {
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
</style>
