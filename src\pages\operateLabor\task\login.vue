<template>
  <div class="login-page">
    <div
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px;
      "
    >
      <img style="width: 100px; height: 100px" :src="logoUrl" alt="" />
    </div>
    <Form style="display: flex; flex-direction: column; gap: 10px">
      <Field
        v-model="loginForm.phone"
        type="tel"
        placeholder="请通过手机号登录"
        maxlength="11"
        clearable
        left-icon="phone-o"
        style="background: #f2f2f2"
      />

      <Captcha v-model="loginForm.captcha" />

      <Field
        v-model="loginForm.smsCode"
        type="text"
        placeholder="请输入验证码"
        maxlength="6"
        left-icon="shield-o"
        style="background: #f2f2f2; width: 300px"
      >
        <template #button>
          <a
            size="small"
            type="text"
            :disabled="smsCountdown > 0"
            @click="sendSmsCode"
            class="sms-btn"
            style="color: #4285f4; cursor: pointer"
          >
            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
          </a>
        </template>
      </Field>

      <Button
        type="primary"
        size="large"
        :disabled="!canLogin"
        @click="handleLogin"
        block
        class="login-btn"
      >
        登录
      </Button>

      <div class="agreement-section">
        <Checkbox v-model="agreedToTerms" class="agreement-checkbox">
          <div style="display: flex; align-items: center">
            <span class="agreement-text"> 阅读并同意 </span>
            <div
              style="display: flex; flex-direction: column"
              v-for="(item, index) in h5ServiceAgreement"
              :key="index"
            >
              <span class="agreement-link" @click="showAgreement(item.fileId)">
                {{ item.name }}
              </span>
            </div>
          </div>
        </Checkbox>
      </div>
    </Form>


  </div>
</template>

<script>
import { Form, Field, Button, Checkbox, Toast } from 'vant'
import Captcha from './captcha.vue'
import { setToken } from 'kit/helpers/token'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'TaskLogin',

  components: {
    Form,
    Field,
    Button,
    Checkbox,
    Toast,
    Captcha
  },

  data() {
    return {
      loginForm: {
        phone: '',
        captcha: {},
        smsCode: '',
        smsCodeToken: ''
      },
      agreedToTerms: false,
      smsCountdown: 0,
      smsTimer: null,
      h5ServiceAgreement: [],
      logoUrl: ''
    }
  },

  async created() {
    var domain = window.location.host
    if (domain.includes('localhost')) {
      domain = '156-dev.olading.com'
    }
    const [err, r] = await client.domainInfo({
      body: {
        domain: domain
      }
    })
    if (err) {
      handleError(err)
      return
    }
    this.h5ServiceAgreement = r.data.h5ServiceAgreement
    this.logoUrl = `${window.env?.apiPath}/api/public/previewFile/${r.data.h5LogoUrl}`
    localStorage.setItem('domainInfo', JSON.stringify(r.data))

    // 检查是否从协议页面返回并已同意
    if (this.$route.query.agreed === 'true') {
      this.agreedToTerms = true
      // 清除URL参数
      this.$router.replace({ path: '/login' })
    }
  },
  computed: {
    canLogin() {
      return (
        this.loginForm.phone.length === 11 &&
        this.loginForm.captcha.value &&
        this.loginForm.captcha.value.length === 4 &&
        this.loginForm.smsCode.length === 6 &&
        this.loginForm.smsCodeToken &&
        this.agreedToTerms
      )
    }
  },

  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    async sendSmsCode() {
      if (this.loginForm.phone.length !== 11) {
        Toast('请输入正确的手机号')
        return
      }
      if (
        !this.loginForm.captcha.value ||
        this.loginForm.captcha.value.length !== 4
      ) {
        Toast('请输入正确的图形验证码')
        return
      }

      // 开始倒计时
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)

      this.sendLoginSms()
    },
    async sendLoginSms() {
      const [err, r] = await client.apiSendLoginSms({
        body: {
          receiver: this.loginForm.phone,
          captchaToken: this.loginForm.captcha.token,
          captchaAnswer: this.loginForm.captcha.value
        }
      })
      if (err) {
        handleError(err)
        clearInterval(this.smsTimer)
        this.smsTimer = null
        this.smsCountdown = 0
        this.loginForm.captcha.value = ''
        return
      }
      this.loginForm.smsCodeToken = r.data.token
    },
    async handleLogin() {
      if (!this.canLogin) return

      const [err, r] = await client.login({
        body: {
          account: this.loginForm.phone,
          code: this.loginForm.smsCode,
          otpToken: this.loginForm.smsCodeToken,
          smsLogin: true,
          captchaToken: this.loginForm.captcha.token,
          captchaAnswer: this.loginForm.captcha.value,
          type: 'PERSONAL'
        }
      })

      if (err) {
        handleError(err)
        return
      }
      setToken(r.data.token)

      // 跳转到实名认证页面
      this.$router.push('/ocr')
    },

    // showAgreement(url) {
    //   // 跳转到协议页面
    //   this.$router.push({
    //     path: '/agreement',
    //     query: {
    //       url: url,
    //       title: '服务协议'
    //     }
    //   })
    // },

    showAgreement(fileId) {
      // 构造文件预览URL并在新标签页中打开
      const previewUrl = `${window.env?.apiPath}/api/public/previewFile/${fileId}`
      window.open(previewUrl, '_blank')
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.login-page {
  min-height: 100vh;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-section {
  margin-bottom: 60px;
  margin-top: 40px;
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  position: relative;
}

.logo-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.logo-waves {
  position: relative;
  width: 60%;
  height: 60%;
}

.wave {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
}

.wave1 {
  width: 100%;
  height: 100%;
  animation: wave 2s infinite;
}

.wave2 {
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
  animation: wave 2s infinite 0.5s;
}

.wave3 {
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
  animation: wave 2s infinite 1s;
}

@keyframes wave {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.login-form {
  width: 100%;
  max-width: 320px;
}

.input-group {
  margin-bottom: 16px;
}

.captcha-image {
  width: 80px;
  height: 32px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.captcha-text {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  letter-spacing: 2px;
}

.sms-btn {
  font-size: 12px;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
}

.password-login {
  text-align: right;
  margin-bottom: 30px;
}

.password-login span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.login-btn {
  margin-bottom: 20px;
  border-radius: 25px;
  height: 50px;
  font-size: 18px;
  font-weight: 600;
}

.agreement-section {
  text-align: center;
}

.agreement-checkbox {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.agreement-text {
  margin: 0 8px 0 8px;
}

.agreement-link {
  color: #87ceeb;
  text-decoration: underline;
  cursor: pointer;
}

/* Vant组件样式覆盖 */
:deep(.van-field) {
  align-items: center;
}

:deep(.van-field) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25px;
  margin-bottom: 16px;
  padding: 0 20px;
}

:deep(.van-field__control) {
  font-size: 16px;
  color: #333;
}

:deep(.van-field__control::placeholder) {
  color: #999;
}

:deep(.van-field__left-icon) {
  margin-right: 10px;
  font-size: 18px;
}

:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
}

:deep(.van-button--primary:disabled) {
  background: #ccc;
  border-color: #ccc;
}

:deep(.van-checkbox__icon) {
  border-color: rgba(255, 255, 255, 0.6);
}

:deep(.van-checkbox__icon--checked) {
  background: #4285f4;
  border-color: #4285f4;
}


</style>
