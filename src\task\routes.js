import Home from './app.vue'
import Login from 'kit/pages/operateLabor/task/login.vue'
import OcrIdentify from 'kit/pages/operateLabor/task/ocrIdentify.vue'
import FaceAuth from 'kit/pages/operateLabor/task/faceAuth.vue'
import Ocr from 'kit/pages/operateLabor/task/ocr.vue'
import LaborContract from 'kit/pages/operateLabor/task/laborContract.vue'
import LaborContractSign from 'kit/pages/operateLabor/task/laborContractSign.vue'
import Agreement from 'kit/pages/operateLabor/task/agreement.vue'

const routes = [
  {
    path: '/',
    component: Home
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '个人登录'
    }
  },
  {
    path: '/ocr',
    component: Ocr,
    meta: {
      title: '实名认证'
    }
  },
  {
    path: '/ocrIdentify',
    component: OcrIdentify,
    meta: {
      title: '实名认证',
    }
  },
  {
    path: '/faceAuth',
    component: FaceAuth,
    meta: {
      title: '人脸识别'
    }
  },
  {
    path: '/laborContract',
    component: LaborContract,
    meta: {
      title: '授权协议'
    }
  },
  {
    path: '/laborContractSign',
    component: LaborContractSign,
    meta: {
      title: '授权协议'
    }
  },
  {
    path: '/agreement',
    component: Agreement,
    meta: {
      title: '服务协议'
    }
  },
]

export default routes
