<template>
  <div class="startSign">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 800px"
    >
      <Title title="选择模板" />
      <el-form-item label="选择模板" prop="templateId">
        <ContractTemplatesSelector
          v-model="form.templateId"
          @change="handleTemplateChange"
        />
      </el-form-item>

      <Title title="基本信息" />
      <el-form-item label="协议名称" prop="protocolName">
        <el-input v-model="form.protocolName" placeholder="请输入协议名称" />
      </el-form-item>

      <el-form-item label="选择作业主体" prop="corporationId">
        <CorporationsSelector
          style="width: 100%"
          v-model="form.corporationId"
          :templateId="form.templateId"
          :corporationIdOptions="corporationIdOptions"
          :multiple="false"
          placeholder="请选择作业主体"
          @change="handleCorporationChange"
        />
      </el-form-item>

      <el-form-item label="选择客户" prop="customerId">
        <SupplierCustomersSelector
          v-model="form.customerId"
          :multiple="false"
          placeholder="请选择客户"
          @change="handleCustomerChange"
        />
      </el-form-item>

      <el-form-item label="选择服务合同" prop="contractId">
        <ServiceContractsSelector
          v-model="form.contractId"
          :corporation-id="form.corporationId"
          :customer-id="form.customerId"
          placeholder="请选择服务合同"
          :disabled="!form.customerId || !form.corporationId"
          @change="handleContractChange"
        />
      </el-form-item>

      <el-form-item label="合同开始时间" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="请选择开始时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="合同结束时间" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="请选择结束时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>

      <Title title="配置流程" />
      <el-form-item label="签署人员" prop="laborInfoIdList">
        <SupplierLaborsSelector
          v-model="form.laborInfoIdList"
          :contractId="form.contractId"
        />
      </el-form-item>

      <el-form-item style="margin-top: 40px">
        <el-button type="primary" @click="onSubmit" :loading="submitting">
          发起签署
        </el-button>
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import CorporationsSelector from './selector/corporations.vue'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
import ContractTemplatesSelector from './selector/contractTemplates.vue'
import SupplierLaborsSelector from './selector/supplierLabors.vue'
import Title from './components/title.vue'
const client = makeClient()

export default {
  name: 'StartSign',
  components: {
    Title,
    CorporationsSelector,
    SupplierCustomersSelector,
    ServiceContractsSelector,
    ContractTemplatesSelector,
    SupplierLaborsSelector
  },
  data() {
    const validateEndDate = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择合同结束时间'))
      }
      if (this.form.startDate && value < this.form.startDate) {
        return callback(new Error('结束时间不能早于开始时间'))
      }
      callback()
    }
    return {
      form: {
        templateId: '',
        protocolName: '',
        corporationId: null,
        customerId: null,
        contractId: null,
        startDate: '',
        endDate: '',
        laborInfoIdList: []
      },
      rules: {
        templateId: [
          { required: true, message: '请选择一个模板', trigger: 'change' }
        ],
        protocolName: [
          { required: true, message: '请输入协议名称', trigger: 'blur' }
        ],
        corporationId: [
          { required: true, message: '请选择一个作业主体', trigger: 'change' }
        ],
        customerId: [
          { required: true, message: '请选择一个客户', trigger: 'change' }
        ],
        contractId: [
          { required: true, message: '请选择一份服务合同', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择合同开始时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, validator: validateEndDate, trigger: 'change' }
        ],
        laborInfoIdList: [
          {
            required: true,
            message: '请至少选择一位签署人员',
            trigger: 'change'
          }
        ]
      },
      submitting: false,
      corporationIdOptions: []
    }
  },
  methods: {
    async handleTemplateChange(template) {
      this.form.corporationId = null
      if (template) {
        this.form.protocolName = template.tempName

        try {
          const [err, response] = await client.getTemplateDetail(
            template.tempId,
            {}
          )

          if (err) {
            handleError(err)
            return
          }

          this.corporationIdOptions = response.data.corporationIds || []
        } catch (error) {
          handleError(error)
        }
      }
    },
    handleCorporationChange() {
      this.form.contractId = null
    },
    handleCustomerChange() {
      this.form.contractId = null
    },
    handleContractChange() {
      this.form.laborInfoIdList = []
    },
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      try {
        const [err, r] = await client.supplierProtocolSignInit({
          body: this.form
        })

        this.submitting = false

        if (err) {
          handleError(err)
          return
        }

        if (r.success) {
          this.$message.success('发起签署成功')
          this.$router.back()
        } else {
          this.$message.error(r.message)
        }
      } catch (error) {
        handleError(error)
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.startSign {
  padding: 20px;
}

.sign-process {
  margin: 20px 0;
}

.sign-parties {
  display: flex;
  gap: 40px;
}

.sign-party {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.party-header {
  background: #f5f7fa;
  padding: 15px 20px;
  font-weight: 500;
  color: #303133;
  text-align: center;
}

.party-content {
  padding: 20px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-signer-btn {
  color: #409eff;
  font-size: 14px;
}

.signers-list {
  margin-top: 15px;
  width: 100%;
}

.signer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.signer-item:last-child {
  margin-bottom: 0;
}

.remove-btn {
  color: #f56c6c;
  padding: 0;
}
</style>
