<template>
  <div class="billingManageDetail" style="height: 100%">
    <div
      style="display: flex; align-items: center; justify-content: space-between"
    >
      <div style="display: flex; margin: 20px 0">
        <div
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100px;
            height: 100px;
            background: #4f71ff;
            color: #fff;
            border-radius: 10px;
          "
        >
          <span>{{ formatterMonth(info.billMonth) }}</span>
          <span>账单月份</span>
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            margin-left: 20px;
            justify-content: space-around;
          "
        >
          <span>{{ info.customerName }}</span>
          <span>所属服务合同：{{ info.contractName }}</span>
        </div>
      </div>
      <span>总费用：{{ info.totalReceivableAmount }}</span>
    </div>
    <div style="text-align: right; margin-bottom: 10px">
      <el-button type="primary" @click="handleImport"
        >导入其他费用</el-button
      >
    </div>
    <el-table
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="feeTypeDesc"
        label="结算项目"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="billMonth"
        label="结算月份"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="personCount"
        label="办理人数"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="totalAmount"
        label="应收金额"
        min-width="160"
      ></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="导入员工其他费用"
      :visible.sync="importVisible"
      width="560px"
      :close-on-click-modal="false"
    >
      <el-upload
        drag
        class="upload-demo"
        action=""
        :http-request="uploadFile"
        :before-upload="beforeUpload"
        :on-change="handleChange"
        :on-remove="handleMove"
        :file-list="fileList"
        :disabled="disabled"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">拖拽文件到这里上传</div>
        <div class="el-upload__text">
          <el-button type="primary" size="mini" style="margin-top: 10px">{{
            isUploadFile ? '重新上传' : '选择文件'
          }}</el-button>
          <div style="padding-top: 10px">
            仅支持格式为xls、xlsx的文件，且文件大小不超过5Mb
          </div>
          <div class="upload-template">
            如有需要也可使用系统模板
            <span
              style="color: #4f71ff; margin-left: 5px; cursor: pointer"
              @click="downloadTemplate"
            >
              <i class="el-icon-download"></i>下载模板
            </span>
          </div>
        </div>
      </el-upload>
      <p v-if="showErrInfo">
        您的数据导入失败，<span
          style="color: green; cursor: pointer"
          @click="downloadErrFile"
          >点击下载错误文件</span
        >
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      info: {
        billMonth: '',
        customerName: '',
        contractName: '',
        totalReceivableAmount: 0
      },
      tableData: [],
      fileList: [],
      importVisible: false,
      isUploadFile: false,
      formData: null,
      loading: false,
      disabled: false,
      showErrInfo: false,
      failCount: 0,
      uuid: ''
    }
  },
  computed: {
    billId() {
      return this.$route.params.id
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      const [err, r] = await client.apiSupplierBillsDetail({
        body: {
          billId: this.billId
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.info.billMonth = r.data.billMonth
      this.info.customerName = r.data.customerName
      this.info.contractName = r.data.contractName
      this.info.totalReceivableAmount = r.data.totalReceivableAmount
      this.tableData = r.data.categories || []
    },
    formatterMonth(value) {
      const year = value.split('-')[0]
      const month = value.split('-')[1]
      return value ? month + ' / ' + year : ''
    },
    handleImport() {
      this.fileList = []
      this.showErrInfo = false
      this.importVisible = true
    },
    //上传模板
    async uploadFile(params) {
      this.formData = new FormData()
      this.formData.set('file', params.file)
    },
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isXls = testmsg === 'xls' || testmsg === 'xlsx'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isXls) {
        this.$message.warning('上传文件类型只能是 xls,xlsx 格式!')
        this.fileList = []
        return false
      }
      if (!isLt5M) {
        this.$message.warning('上传文件大小不能超过 5MB!')
        this.fileList = []
        return false
      }
    },

    handleMove() {
      this.fileList = []
      this.isUploadFile = false
    },
    handleChange(file, fileList) {
      this.fileList = [file]
      this.isUploadFile = true
    },
    //模板下载
    async downloadTemplate() {
      this.disabled = true
      const result = await client.apiSupplierBillsImportTemplate()
      this.disabled = false
      await exportExcel(result)
    },
    cancel() {
      this.importVisible = false
    },
    async handleConfirm() {
      if (!this.fileList.length) {
        this.$message.warning('请上传文件')
        return
      }
      this.loading = true
      this.formData.set('billId', this.billId)
      const [err, r] = await client.apiSupplierBillsImportPreview({
        body: this.formData,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      if(!r.data.success) {
        this.uuid = r.data.uuid
        this.showErrInfo = true
        return
      }
      this.importVisible = false
      this.$message.success('导入成功')
      this.fetchData()
    },
    async downloadErrFile() {
      const result = await client.supplierBillsImportVerifyErrorLog(this.uuid)
      await exportExcel(result)
    },
    handleView(row) {
      const { billMasterId, feeType } = row
      if (feeType === 'SALARY') {
        this.$router.push({
          path: '/billingManageDetail/salary',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      } else if (feeType === 'MANAGEMENT_FEE') {
        this.$router.push({
          path: '/billingManageDetail/managementFee',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      } else {
        this.$router.push({
          path: '/billingManageDetail/otherFee',
          query: {
            billMasterId: billMasterId,
            contractName: this.info.contractName,
            rowData: JSON.stringify(row)
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.upload-demo {
  width: 500px;
  margin: 0 auto;
  position: relative;
}
.upload-template {
  position: absolute;
  bottom: 10px;
  left: 80px;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-upload-dragger {
  width: 500px;
  height: 230px;
  border: 1px dashed #d9d9d9;
}
</style>
