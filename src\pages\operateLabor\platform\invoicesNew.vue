<template>
  <div class="invoicesNew">
    <!-- Step 1: Preset Info Form -->
    <div>
      <Title title="开票信息" />
      <el-form
        ref="presetForm"
        :model="presetForm"
        :rules="presetRules"
        label-width="100px"
        style="max-width: 500px; margin-top: 20px"
        :disabled="step === 2"
      >
        <el-form-item label="服务合同" prop="contractId">
          <el-select
            filterable
            v-model="presetForm.contractId"
            placeholder="请选择服务合同"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in contractOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账单月份" prop="billMonth">
          <el-date-picker
            v-model="presetForm.billMonth"
            type="month"
            placeholder="请选择账单月份"
            format="yyyy-MM"
            value-format="yyyy-MM-01"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="step === 1">
          <el-button
            type="primary"
            @click="handlePresetSubmit"
            :loading="loadingPreset"
            >申请</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- Step 2: Full Application Form -->
    <div v-if="step === 2">
      <el-form
        ref="applyForm"
        :model="applyForm"
        :rules="applyRules"
        label-width="120px"
      >
        <Title title="基本信息" />
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item label="客户" prop="customerId">
              <SupplierCustomersSelector
                v-model="applyForm.customerId"
                disabled
                :multiple="false"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票主体" prop="supplierCorporationId">
              <el-select
              v-model="applyForm.supplierCorporationId"
              placeholder="请选择开票主体"
              style="width: 100%"
              clearable
              filterable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票类型" prop="type">
              <el-select
                v-model="applyForm.type"
                placeholder="请选择发票类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in invoiceTypeOptions"
                  :key="item.type"
                  :label="item.description"
                  :value="item.type"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <Title title="发票信息" />
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item label="发票抬头" prop="title">
              <el-input
                v-model="applyForm.title"
                placeholder="请输入发票抬头"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="纳税人识别号" prop="taxNo">
              <el-input
                v-model="applyForm.taxNo"
                placeholder="请输入纳税人识别号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行" prop="bankName">
              <el-input
                v-model="applyForm.bankName"
                placeholder="请输入开户行"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input
                v-model="applyForm.bankAccount"
                placeholder="请输入银行账号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="注册地址" prop="registerAddress">
              <el-input
                v-model="applyForm.registerAddress"
                placeholder="请输入注册地址"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业电话" prop="companyTel">
              <el-input
                v-model="applyForm.companyTel"
                placeholder="请输入企业电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="发票备注" prop="remark">
              <el-input
                v-model="applyForm.remark"
                placeholder="请输入发票备注"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <Title title="开票明细" />
        <el-table
          :data="applyForm.items"
          style="width: 100%; margin-top: 20px"
          size="small"
          show-summary
          :summary-method="getSummaries"
          :header-cell-style="{
            'font-size': '12px',
            'font-weight': '400',
            color: '#777c94',
            background: 'var(--o-primary-bg-color)'
          }"
        >
          <el-table-column
            prop="billNo"
            label="账单编号"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="billMonth"
            label="账单月份"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="customerName"
            label="所属客户"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="contractName"
            label="所属服务合同"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="totalInvoiceAmount"
            label="账单金额"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="availableAmount"
            label="可开票金额"
            width="120"
          ></el-table-column>
          <el-table-column label="发票类目" width="200">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.invoiceCategory"
                placeholder="请选择类目"
              >
                <el-option
                  v-for="cat in scope.row.availableCategories"
                  :key="cat"
                  :label="cat"
                  :value="cat"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="开票金额" width="150">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.fee"
                :precision="2"
                :min="0.01"
                :max="scope.row.availableAmount"
                size="small"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRemoveItem(scope.$index)"
                >移除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div style="text-align: center; margin-top: 20px">
          <el-button
            type="primary"
            @click="handleApplySubmit"
            :loading="loadingApply"
            >提交申请</el-button
          >
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import CorporationsSelector from './selector/corporations.vue'

const client = makeClient()

export default {
  name: 'InvoicesNew',
  components: {
    Title,
    ServiceContractsSelector,
    SupplierCustomersSelector,
    CorporationsSelector
  },
  data() {
    return {
      step: 1,
      loadingPreset: false,
      loadingApply: false,

      presetForm: {
        contractId: null,
        billMonth: ''
      },
      presetRules: {
        contractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        billMonth: [
          { required: true, message: '请选择账单月份', trigger: 'change' }
        ]
      },
      contractOptions: [],
      supplierOptions: [],
      invoiceTypeOptions: [],
      applyForm: {
        customerId: null,
        contractId: null,
        supplierCorporationId: null,
        type: '',
        title: '',
        taxNo: '',
        bankName: '',
        bankAccount: '',
        registerAddress: '',
        companyTel: '',
        remark: '',
        applyRemark: '',
        addresseeName: '',
        addresseeMobile: '',
        addresseeAddress: '',
        addresseeEmail: '',
        items: []
      },
      applyRules: {
        customerId: [{ required: true, message: '客户不能为空' }],
        contractId: [{ required: true, message: '合同不能为空' }],
        supplierCorporationId: [
          { required: true, message: '开票主体不能为空' }
        ],
        type: [
          { required: true, message: '请选择发票类型', trigger: 'change' }
        ],
        title: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
        taxNo: [
          { required: true, message: '请输入纳税人识别号', trigger: 'blur' }
        ],
        addresseeName: [
          { required: true, message: '请输入收件人姓名', trigger: 'blur' }
        ],
        addresseeMobile: [
          { required: true, message: '请输入收件人电话', trigger: 'blur' }
        ],
        addresseeAddress: [
          { required: true, message: '请输入收件人地址', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    await this.loadContractOptions()
    await this.loadSupplierOptions()
  },
  methods: {
    // 加载合同选项
    async loadContractOptions() {
      try {
        const [err, response] = await client.supplierListContract({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.contractOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
      }
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },
    handlePresetSubmit() {
      this.$refs.presetForm.validate(async valid => {
        if (valid) {
          this.loadingPreset = true
          try {
            const [err, res] = await client.supplierInvoicesPresetInfo({
              body: this.presetForm
            })
            if (err) {
              handleError(err)
              return
            }
            this.populateApplyForm(res.data)
            this.step = 2
          } finally {
            this.loadingPreset = false
          }
        }
      })
    },
    populateApplyForm(data) {
      this.applyForm.customerId = data.customerId
      this.applyForm.contractId = data.contractId
      this.applyForm.supplierCorporationId = data.supplierCorporationId

      this.invoiceTypeOptions = data.invoiceTypes || []
      if (this.invoiceTypeOptions.length > 0) {
        this.applyForm.type = this.invoiceTypeOptions[0].type
      }

      if (data.invoiceInfo) {
        this.applyForm.title = data.invoiceInfo.title
        this.applyForm.taxNo = data.invoiceInfo.taxNo
        this.applyForm.bankName = data.invoiceInfo.bankName
        this.applyForm.bankAccount = data.invoiceInfo.bankAccount
        this.applyForm.registerAddress = data.invoiceInfo.registerAddress
        this.applyForm.companyTel = data.invoiceInfo.companyTel
        this.applyForm.remark = data.invoiceInfo.remark
      }

      this.applyForm.items = (data.availableBills || []).map(bill => ({
        ...bill,
        // Set default values for the editable fields in the table
        invoiceCategory:
          bill.availableCategories && bill.availableCategories.length > 0
            ? bill.availableCategories[0]
            : null,
        fee: bill.availableAmount
      }))
    },
    handleRemoveItem(index) {
      this.applyForm.items.splice(index, 1)
    },
    handleApplySubmit() {
      if (this.applyForm.items.length === 0) {
        this.$message.error('开票明细不能为空')
        return
      }

      this.$refs.applyForm.validate(async valid => {
        if (valid) {
          this.loadingApply = true
          try {
            const [err, res] = await client.supplierInvoicesApply({
              body: this.applyForm
            })
            if (err) {
              handleError(err)
              return
            }
            this.$message.success('发票申请提交成功')
            this.$router.push('/invoices')
          } finally {
            this.loadingApply = false
          }
        }
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          if (
            column.property === 'totalInvoiceAmount' ||
            column.property === 'availableAmount' ||
            column.property === 'fee'
          ) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = sums[index].toFixed(2)
          } else {
            sums[index] = ''
          }
        } else {
          sums[index] = ''
        }
      })

      return sums
    }
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 0;
}
</style>
