<template>
  <div class="rolesSelector">
    <el-select
      v-model="selectedRoleIds"
      :multiple="multiple"
      filterable
      clearable
      remote
      :remote-method="remoteMethod"
      placeholder="请输入角色名称"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      @change="handleChange"
    >
      <el-option
        v-for="item in roles"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'RolesSelector',
  props: {
    multiple: {
      type: Boolean,
      default: true
    },
    value: {
      type: Array | Number,
      default: () => {
        if (this.multiple) {
          return []
        } else {
          return null
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      roles: [], // Roles from search results
      selectedRoleIds: []
    }
  },
  async created() {
    await this.fetchRoles()
    if (!this.value) {
      return
    }
    for (var i = 0; i < this.value.length; i++) {
      this.value[i] = this.value[i] * 1
    }

    if (this.value.length > 0) {
      await this.fetchRolesByIds(this.value)
    }
    setTimeout(() => {
      this.selectedRoleIds = this.value
    }, 300)
  },
  methods: {
    reset() {
      this.selectedRoleIds = []
    },
    async fetchRoles(name) {
      this.loading = true
      const [err, r] = await client.listRoles({
        body: {
          offset: 0,
          limit: 1000, // Reset limit to 1000 for remote search
          withDisabled: false,
          filters: {
            name: name || ''
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }
      for (var c of r.data.list) {
        if (!this.roles.find(item => item.id === c.id)) {
          this.roles.push(c)
        }
      }
    },
    async fetchRolesByIds(roleIds) {
      const requests = []
      for (var c of roleIds) {
        if (!this.roles.find(item => item.id === c)) {
          requests.push(client.roleDetail({ body: { roleId: id } }))
        }
      }
      if (requests.length === 0) return

      const results = await Promise.all(requests)
      for (var c of results) {
        const [err, r] = c
        if (err) {
          handleError(err)
          continue
        }
        this.selectedRoleIds.push(r.data.id)
        this.roles.push(r.data)
      }
    },
    remoteMethod(query) {
      this.fetchRoles(query)
    },
    handleChange(value) {
      this.$emit('input', value)
    }
  }
}
</script>
